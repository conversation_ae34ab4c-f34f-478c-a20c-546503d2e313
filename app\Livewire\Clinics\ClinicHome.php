<?php

namespace App\Livewire\Clinics;

use App\Models\Appointment;
use App\Models\Clinic;
use Livewire\Component;
use Livewire\WithPagination;

class ClinicHome extends Component
{
    use WithPagination;
    public $search = '';
    public $sortField = 'appointment_date';
    public $sortDirection = 'asc';
    public $paginate = 10;
    private $routeName = 'clinics/clinic';
    public $viewName;
    public $clinic;
    public $appointments;


    private function prepareAppointments()
    {
        return app('livewire')->mount('appointments.appointments', [
            'appointments' => $this->clinic->appointments()->orderBy('appointment_date', 'desc')->get(),
            'title' => $this->clinic->name . ' > Appointments'
        ]);
    }


    public function mount($clinic_id)
    {
        $clinic = Clinic::find($clinic_id);

        $this->clinic = $clinic;
    }

    public function render()
    {
        if (!$this->clinic) {
            return abort(404);
        }

        // $this->routeName = request()->route()->getName();

        // if ($this->routeName == 'clinics/appointments') {
        //     return $this->prepareAppointments();
        // } else if ($this->routeName == 'clinics/doctors') {
        //     return $this->prepareDoctors();
        // } else if ($this->routeName == 'clinics/services') {
        //     return $this->prepareServices();
        // } else if ($this->routeName == 'clinics/offers') {
        //     return $this->prepareOffers();
        // }

        $this->clinic->load(['business', 'doctors', 'services']);
        $appointments = Appointment::with(['customer', 'doctor'])
            ->where('clinic_id', $this->clinic->id)
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->paginate);

        $header = ['title' => 'All Clinics', 'subtitle' => $this->clinic->name];

        return view('livewire.clinics.clinic-home', compact('appointments', 'header'))->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Clinics | ' . $this->clinic->name,
            ]
        );
    }
}
