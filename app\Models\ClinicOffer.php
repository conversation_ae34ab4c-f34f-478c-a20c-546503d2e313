<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClinicOffer extends Model
{

    protected $fillable = [
        'clinic_id',
        'title',
        'description',
        'offer_url',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }
}
