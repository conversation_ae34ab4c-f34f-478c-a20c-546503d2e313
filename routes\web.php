<?php

use App\Http\Controllers\Api\WhatsAppController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



Route::middleware('guest')->group(function () {
    Route::get('/login', [\App\Http\Controllers\AuthController::class, 'loginView'])->name('loginView');
    Route::post('/login', [\App\Http\Controllers\AuthController::class, 'login'])->name('login');
    Route::get('/register', [\App\Http\Controllers\AuthController::class, 'registerView'])->name('registerView');
    Route::post('/register', [\App\Http\Controllers\AuthController::class, 'register'])->name('register');
});


Route::middleware('auth')->group(function () {

    Route::post('/logout', [\App\Http\Controllers\AuthController::class, 'logout'])->name('logout');

    Route::redirect('/dashboard', '/')->name('dashboard');
    Route::view('/', 'pages/dashboard')->name('dashboard');

    Route::get('/settings', \App\Livewire\Settings\SettingsPage::class)->name('settings');

    // WhatsApp attachment serving route
    Route::get('/whatsapp/attachments/{filename}', function ($filename) {
        $path = storage_path('app/public/whatsapp/attachments/' . $filename);

        if (!file_exists($path)) {
            abort(404);
        }

        return response()->file($path);
    })->name('whatsapp.attachment');

    Route::view('/social-media-bots', 'pages.social-media')->name('social/social-media');
    Route::get('/social-media-bots/whatsapp', \App\Livewire\SocialBots\Whatsapp::class)->name('social/whatsapp');
    Route::get('/social-media-bots/telegram', \App\Livewire\SocialBots\Telegram::class)->name('social/telegram');
    Route::get('/social-media-bots/instagram', \App\Livewire\SocialBots\Instagram::class)->name('social/instagram');
    Route::get('/social-media-bots/discord', \App\Livewire\SocialBots\Discord::class)->name('social/discord');
    Route::get('/social-media-bots/facebook', \App\Livewire\SocialBots\Facebook::class)->name('social/facebook');
    Route::get('/social-media-bots/tiktok', \App\Livewire\SocialBots\TikTok::class)->name('social/tiktok');
    Route::get('/social-media-bots/snapchat', \App\Livewire\SocialBots\Snapchat::class)->name('social/snapchat');

    Route::view('/clinics', 'pages.clinics')->name('clinics/clinics');
    Route::get('/clinics/clinic/{clinic_id}', \App\Livewire\Clinics\ClinicHome::class)->name('clinics/clinic');
    Route::get('/clinics/clinic/edit-clinic/{clinic_id}', \App\Livewire\Clinics\ClinicForm::class)->name('clinics/edit-clinic');
    Route::get('/clinics/clinic/new-clinic', \App\Livewire\Clinics\ClinicForm::class)->name('clinics/new-clinic');

    Route::get('/clinics/services/{clinic_id}', \App\Livewire\Services\Services::class)->name('clinics/services');
    Route::get('/clinics/offers/{clinic_id}', \App\Livewire\Offers\Offers::class)->name('clinics/offers');
    Route::get('/clinics/doctors/{clinic_id}', \App\Livewire\Doctors\Doctors::class)->name('clinics/doctors');
    Route::get('/clinics/appointments/{clinic_id}', \App\Livewire\Clinics\ClinicHome::class)->name('clinics/appointments');

    Route::view('/doctors', 'pages.doctors')->name('doctors/doctors');
    Route::get('/doctors/doctor/{doctor_id}', \App\Livewire\Doctors\DoctorHome::class)->name('doctors/doctor');
    Route::get('/doctors/doctor/edit-doctor/{doctor_id}', \App\Livewire\Doctors\DoctorForm::class)->name('doctors/edit-doctor');
    Route::get('/doctors/doctor/new-doctor', \App\Livewire\Doctors\DoctorForm::class)->name('doctors/new-doctor');

    Route::view('/services', 'pages.services')->name('services/services');
    Route::get('/services/service/{service_id}', \App\Livewire\Services\ServicesHome::class)->name('services/service');
    // Route::get('/services/service/new-service', \App\Livewire\Services\ServiceForm::class)->name('services/new-service');
    // Route::get('/services/service/edit-service/{service_id}', \App\Livewire\Services\ServiceForm::class)->name('services/edit-service');

    Route::view('/appointments', 'pages.appointments')->name('appointments/appointments');
    // Route::get('/appointments/appointment/{appointment_id}', \App\Livewire\Appointments\AppointmentForm::class)->name('appointments/appointment');
    // Route::get('/appointments/appointment/new-appointment', \App\Livewire\Appointments\AppointmentForm::class)->name('appointments/new-appointment');
});
