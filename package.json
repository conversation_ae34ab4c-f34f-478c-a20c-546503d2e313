{"name": "alhars", "version": "3.1.0", "$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "axios": "^1.9.0", "concurrently": "^9.1.2", "laravel-echo": "^2.1.6", "laravel-vite-plugin": "^1.3.0", "postcss": "^8.5.4", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "pusher-js": "^8.4.0", "tailwindcss": "^4.1.10", "vite": "^6.3.5"}, "dependencies": {"@alpinejs/collapse": "^3.14.9", "@alpinejs/intersect": "^3.14.9", "@alpinejs/persist": "^3.14.9", "@caneara/iodine": "^8.5.0", "@fortawesome/fontawesome-free": "^6.7.2", "@popperjs/core": "^2.11.8", "alpinejs": "^3.14.9", "apexcharts": "^4.7.0", "cleave.js": "^1.6.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "express": "^5.1.0", "filepond": "^4.32.7", "filepond-plugin-image-preview": "^4.6.12", "flatpickr": "^4.6.13", "gridjs": "^6.2.0", "highlight.js": "^11.11.1", "node-fetch": "^3.3.2", "quill": "^2.0.3", "simplebar": "^6.3.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.6", "sweetalert2": "^11.22.2", "swiper": "^11.2.8", "tippy.js": "^6.3.7", "toastify-js": "^1.12.0", "tom-select": "^2.4.3", "whatsapp-web.js": "^1.30.0"}, "description": "<p align=\"center\"><a href=\"https://laravel.com\" target=\"_blank\"><img src=\"https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg\" width=\"400\" alt=\"Laravel Logo\"></a></p>", "main": "vite.config.js", "directories": {"example": "examples", "test": "tests"}, "keywords": [], "author": "", "license": "ISC"}