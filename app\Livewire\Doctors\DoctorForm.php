<?php

namespace App\Livewire\Doctors;

use App\Models\Clinic;
use App\Models\Doctor;
use Livewire\Component;
use Livewire\WithFileUploads;

class DoctorForm extends Component
{
    use WithFileUploads;

    public $clinics;
    public $commingFromClinic = false;

    public $doctor_id;
    public $clinic_id;
    public $name;
    public $profile_image;
    public $specialization;
    public $experience;
    public $education;
    public $certifications;
    public $languages;
    public $is_available;
    public $unavailability_reason;
    public $working_days;
    public $shift;

    public function mount($doctor = null, $clinic_id = null)
    {
        if (!$this->clinics) {
            $this->clinics = Clinic::select('id', 'name')->get();
        }

        if ($clinic_id) {
            $this->commingFromClinic = true;
            $this->clinic_id = $clinic_id;
        }

        if ($doctor) {
            $this->doctor_id = $doctor['id'];
            $this->clinic_id = $doctor['clinic_id'];
            $this->name = $doctor['name'];
            $this->profile_image = $doctor['profile_image'];
            $this->specialization = $doctor['specialization'];
            $this->experience = $doctor['experience'];
            $this->education = $doctor['education'];
            $this->certifications = $doctor['certifications'];
            $this->languages = $doctor['languages'];
            $this->is_available = $doctor['is_available'];
            $this->unavailability_reason = $doctor['unavailability_reason'];
            $this->working_days = $doctor['working_days'];
            $this->shift = $doctor['shift'];
        }
    }

    private function handleImageUpload()
    {
        if ($this->image) {
            $imagePath = $this->image->store('doctors', 'public');
            return $imagePath;
        }
        return null;
    }

    public function save()
    {
        $this->validate([
            'name' => 'required|string|max:100',
            'specialization' => 'required|string|max:100',
            'clinic_id' => 'required|integer',
            'experience' => 'nullable|string',
            'education' => 'nullable|string',
            'certifications' => 'nullable|string',
            'languages' => 'nullable|string',
            'is_available' => 'required|boolean',
            'unavailability_reason' => 'nullable|string',
            'working_days' => 'nullable|string',
            'shift' => 'required|string',
        ]);

        try {
          

            if ($this->doctor_id) {
                Doctor::where('id', $this->doctor_id)->update([
                    'name' => $this->name,
                    'specialization' => $this->specialization,
                    'clinic_id' => $this->clinic_id,
                    'experience' => $this->experience,
                    'education' => $this->education,
                    'certifications' => $this->certifications,
                    'languages' => $this->languages,
                    'is_available' => $this->is_available,
                    'unavailability_reason' => $this->unavailability_reason,
                    'working_days' => $this->working_days,
                    'shift' => $this->shift,
                ]);
            } else {
                Doctor::create([
                    'name' => $this->name,
                    'specialization' => $this->specialization,
                    'clinic_id' => $this->clinic_id,
                    'experience' => $this->experience,
                    'education' => $this->education,
                    'certifications' => $this->certifications,
                    'languages' => $this->languages,
                    'is_available' => $this->is_available,
                    'unavailability_reason' => $this->unavailability_reason,
                    'working_days' => $this->working_days,
                    'shift' => $this->shift,
                ]);
            }
        } catch (\Throwable $th) {
            $this->dispatch('show-error', 'Failed to save doctor');
            return;
        }

        $this->dispatch('doctor-saved', $this->commingFromClinic ? $this->clinic_id : null);

        $this->dispatch('show-success', 'Doctor saved successfully');
    }

    public function render()
    {
        return view('livewire.doctors.doctor-form')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Doctor Form',
            ]
        );
    }
}
