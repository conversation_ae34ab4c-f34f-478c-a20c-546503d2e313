<?php

namespace App\Livewire\Doctors;

use App\Models\Clinic;
use App\Models\Doctor;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class DoctorForm extends Component
{
    use WithFileUploads;

    public $showModal = false;
    public $clinics;
    public $commingFromClinic = false;

    public $doctor_id;
    public $business_id;
    public $clinic_id;
    public $name;
    public $profile_image;
    public $current_profile_image;
    public $specialization;
    public $experience;
    public $education;
    public $certifications = [];
    public $languages = [];
    public $is_available = true;
    public $unavailability_reason;
    public $working_days = [];
    public $shift = 'صباحي';

    // For UI
    public $certification_input = '';
    public $language_input = '';

    protected $specializations = [
        'طب الأسنان العام',
        'جراحة الفم والوجه والفكين',
        'تقويم الأسنان',
        'طب أسنان الأطفال',
        'علاج الجذور',
        'أمراض اللثة',
        'تجميل الأسنان',
        'زراعة الأسنان',
        'طب الأسنان التعويضي',
        'أخرى'
    ];

    protected $availableLanguages = [
        'العربية',
        'الإنجليزية',
        'الفرنسية',
        'الألمانية',
        'الإسبانية',
        'التركية',
        'الفارسية',
        'الأردية',
        'الهندية'
    ];

    protected $workingDaysOptions = [
        'SUN' => 'الأحد',
        'MON' => 'الاثنين',
        'TUE' => 'الثلاثاء',
        'WED' => 'الأربعاء',
        'THU' => 'الخميس',
        'FRI' => 'الجمعة',
        'SAT' => 'السبت'
    ];

    #[On('showDoctorForm')]
    public function showForm($doctor = null)
    {
        $this->reset(['doctor_id', 'name', 'profile_image', 'current_profile_image', 'specialization',
                     'experience', 'education', 'certifications', 'languages', 'unavailability_reason',
                     'working_days', 'certification_input', 'language_input']);

        $this->is_available = true;
        $this->shift = 'صباحي';
        $this->certifications = [];
        $this->languages = [];
        $this->working_days = [];
        $this->showModal = true;

        if ($doctor) {
            $this->doctor_id = $doctor['id'];
            $this->clinic_id = $doctor['clinic_id'];
            $this->name = $doctor['name'];
            $this->current_profile_image = $doctor['profile_image'];
            $this->specialization = $doctor['specialization'];
            $this->experience = $doctor['experience'];
            $this->education = $doctor['education'];
            $this->certifications = $doctor['certifications'] ?? [];
            $this->languages = $doctor['languages'] ?? [];
            $this->is_available = $doctor['is_available'];
            $this->unavailability_reason = $doctor['unavailability_reason'];
            $this->working_days = $doctor['working_days'] ?? [];
            $this->shift = $doctor['shift'];
        }
    }

    public function mount($clinic_id = null)
    {
        $this->clinics = Clinic::select('id', 'name')->get();
        $this->business_id = auth()->user()->business->id ?? 1;

        if ($clinic_id) {
            $this->commingFromClinic = true;
            $this->clinic_id = $clinic_id;
        }
    }

    public function addCertification()
    {
        if (!empty($this->certification_input)) {
            $this->certifications[] = $this->certification_input;
            $this->certification_input = '';
        }
    }

    public function removeCertification($index)
    {
        unset($this->certifications[$index]);
        $this->certifications = array_values($this->certifications);
    }

    public function addLanguage()
    {
        if (!empty($this->language_input) && !in_array($this->language_input, $this->languages)) {
            $this->languages[] = $this->language_input;
            $this->language_input = '';
        }
    }

    public function removeLanguage($index)
    {
        unset($this->languages[$index]);
        $this->languages = array_values($this->languages);
    }



    public function save()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'specialization' => 'required|string|max:255',
            'clinic_id' => 'required|exists:clinics,id',
            'experience' => 'nullable|string|max:255',
            'education' => 'nullable|string|max:500',
            'is_available' => 'boolean',
            'unavailability_reason' => 'nullable|string|max:255',
            'shift' => 'required|in:صباحي,مسائي',
            'profile_image' => 'nullable|image|max:2048',
        ]);

        try {
            $data = [
                'business_id' => $this->business_id,
                'name' => $this->name,
                'specialization' => $this->specialization,
                'clinic_id' => $this->clinic_id,
                'experience' => $this->experience,
                'education' => $this->education,
                'certifications' => $this->certifications,
                'languages' => $this->languages,
                'is_available' => $this->is_available,
                'unavailability_reason' => $this->unavailability_reason,
                'working_days' => $this->working_days,
                'shift' => $this->shift,
            ];

            // Handle image upload
            if ($this->profile_image) {
                if ($this->doctor_id && $this->current_profile_image) {
                    Storage::disk('public')->delete($this->current_profile_image);
                }
                $data['profile_image'] = $this->profile_image->store('doctors', 'public');
            }

            if ($this->doctor_id) {
                Doctor::where('id', $this->doctor_id)->update($data);
                $message = 'تم تحديث بيانات الطبيب بنجاح';
            } else {
                Doctor::create($data);
                $message = 'تم إضافة الطبيب بنجاح';
            }

            $this->showModal = false;
            $this->dispatch('doctor-saved');
            $this->dispatch('show-success', $message);

        } catch (\Throwable $e) {
            $this->dispatch('show-error', 'حدث خطأ أثناء حفظ البيانات');
        }
    }

    public function getSpecializationsProperty()
    {
        return $this->specializations;
    }

    public function getAvailableLanguagesProperty()
    {
        return $this->availableLanguages;
    }

    public function getWorkingDaysOptionsProperty()
    {
        return $this->workingDaysOptions;
    }

    public function render()
    {
        return view('livewire.doctors.doctor-form')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Doctor Form',
            ]
        );
    }
}
