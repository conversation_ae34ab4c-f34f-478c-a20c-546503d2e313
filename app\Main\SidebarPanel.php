<?php

namespace App\Main;

use App\Models\Clinic;

class SidebarPanel
{
    public static function bots()
    {
        return [
            'title' => 'Social Bots',
            'items' => [
                [
                    'bots_whatsapp' => [
                        'title' => 'Whatsapp',
                        'route_name' => 'social/whatsapp'
                    ],
                    'bots_telegram' => [
                        'title' => 'Telegram',
                        'route_name' => 'social/telegram'
                    ],
                    'bots_instagram' => [
                        'title' => 'Instagram',
                        'route_name' => 'social/instagram'
                    ],
                    'bots_discord' => [
                        'title' => 'Discord',
                        'route_name' => 'social/discord'
                    ],
                    'bots_facebook' => [
                        'title' => 'Facebook',
                        'route_name' => 'social/facebook'
                    ],
                    'bots_tiktok' => [
                        'title' => 'TikTok',
                        'route_name' => 'social/tiktok'
                    ],
                    'bots_snapchat' => [
                        'title' => 'Snapchat',
                        'route_name' => 'social/snapchat'
                    ],
                ],
            ]
        ];
    }

    public static function clinics()
    {
        $clinics = auth()->user()->business
            ?->clinics()
            ->withCount('doctors')
            ->get() ?? collect();


        $clinicItems = [];

        foreach ($clinics as $clinic) {
            $clinicItems['clinic_' . $clinic->id] = [
                'title' => $clinic->name,
                'submenu' => [
                    'clinic' => [
                        'title' => 'Clinic',
                        'route_name' => 'clinics/clinic',
                        'params' => ['clinic_id' => $clinic->id]
                    ],
                    'Appointments' => [
                        'title' => 'Appointments',
                        'route_name' => 'clinics/appointments',
                        'params' => ['clinic_id' => $clinic->id]
                    ],
                    'Doctors' => [
                        'title' => 'Doctors',
                        'route_name' => 'clinics/doctors',
                        'params' => ['clinic_id' => $clinic->id]
                    ],
                    'Services' => [
                        'title' => 'Services',
                        'route_name' => 'clinics/services',
                        'params' => ['clinic_id' => $clinic->id]
                    ],
                    'Offers' => [
                        'title' => 'Offers',
                        'route_name' => 'clinics/offers',
                        'params' => ['clinic_id' => $clinic->id]
                    ],
                ]
            ];
        }

        return [
            'title' => 'Clinics',
            'clinics' => $clinics,
            'items' => [
                [
                    'clinics_clinics' => [
                        'title' => 'All Clinics',
                        'route_name' => 'clinics/clinics'
                    ],

                    'clinics_add_clinic' => [
                        'title' => 'Add New Clinic',
                        'route_name' => 'clinics/new-clinic'
                    ],
                ],
                $clinicItems
            ]
        ];
    }

    public static function doctors()
    {
        return [
            'title' => 'Doctors',
            'items' => [
                [
                    'doctors_doctors' => [
                        'title' => 'Doctors',
                        'route_name' => 'doctors/doctors'
                    ],
                ],
            ]
        ];
    }
    public static function services()
    {
        return [
            'title' => 'Services',
            'items' => [
                [
                    'services_services' => [
                        'title' => 'Services',
                        'route_name' => 'services/services'
                    ],
                ],
            ]
        ];
    }
    public static function appointments()
    {
        return [
            'title' => 'Appointments',
            'items' => [
                [
                    'appointments_appointments' => [
                        'title' => 'Appointments',
                        'route_name' => 'appointments/appointments'
                    ],
                ],
            ]
        ];
    }

    public static function all()
    {
        return [
            self::bots(),
            self::clinics(),
            self::doctors(),
            self::services(),
            self::appointments(),
        ];
    }
}
