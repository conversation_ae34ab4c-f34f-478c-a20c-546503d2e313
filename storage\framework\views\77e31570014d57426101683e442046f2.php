<div class="contents">
    <main class="main-content w-full pb-8">
        <div class="flex items-center justify-between space-x-4 py-5 lg:py-6 px-[var(--margin-x)]">
            <div class="flex items-center space-x-4">
                <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl">
                    <a class="text-primary transition-colors hover:text-primary-focus dark:text-accent-light dark:hover:text-accent"
                        href="javascript:history.back()"><?php echo e($header['title']); ?></a>
                </h2>
                <div class="h-full py-1 sm:flex">
                    <div class="h-full w-px bg-slate-300 dark:bg-navy-600"></div>
                </div>
                <ul class="flex-wrap items-center space-x-2 sm:flex">
                    <li><?php echo e($header['subtitle']); ?></li>
                </ul>
            </div>
            <a href="javascript:;"
                class="btn lg:space-x-2 bg-gradient-to-br from-purple-500 to-indigo-600 font-medium text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span class="hidden lg:inline">New Service</span>
            </a>

        </div>

        <div x-data="{
            services: <?php echo e($services); ?>

        }"
            class="mt-8 grid grid-cols-1 px-[var(--margin-x)] gap-4 sm:grid-cols-2 sm:gap-5 lg:grid-cols-3 lg:gap-6 xl:grid-cols-4">
            <template x-for="service in services" :key="service.id">
                <div class="contents">
                    <div class="card justify-between p-4 sm:p-5">
                        <div class="flex items-center space-x-3">
                            <img class="size-10 shrink-0 rounded-lg object-cover"
                                src="<?php echo e(asset('images/800x600.png')); ?>" alt="image" />
                            <div>
                                <h3 class="text-base font-medium text-slate-700 dark:text-navy-100"
                                    x-text="service.name">
                                </h3>
                                <p class="text-xs" x-text="service.description"></p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-xs-plus">Price</p>
                            <p class="text-xl font-medium text-slate-700 dark:text-navy-100" x-text="service.price">
                            </p>
                            <div class="mt-5 flex items-center justify-between space-x-2">
                                <div class="flex items-center justify-between">
                                    <div class="space-x-1.5">
                                        <h1 wire:loading wire:target="showServiceForm">Loading...</h1>
                                        <button wire:loading.remove wire:target="showServiceForm"
                                            x-on:click="window.Livewire.find('<?php echo e($_instance->getId()); ?>').dispatch('showServiceForm', {service: service})"
                                            class="btn size-8 rounded-full bg-slate-150 p-0 font-medium text-slate-800 hover:bg-slate-200 focus:bg-slate-200 active:bg-slate-200/80 dark:bg-navy-500 dark:text-navy-50 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                                            <i class="fa-solid fa-edit"></i>
                                        </button>
                                        <button
                                            x-on:click.prevent="$dialogs.confirmAction({
                                            title: 'Delete Service?',
                                            text: 'Are you sure you want to delete this service?',
                                            onConfirm: () => {
                                                window.Livewire.find('<?php echo e($_instance->getId()); ?>').deleteService(service.id).then((result) => {
                                                    if (result) {
                                                        services = services.filter(s => s.id != service.id);
                                                    }
                                                })
                                            }})"
                                            class="btn size-8 rounded-full bg-error p-0 font-medium text-white hover:bg-error-focus focus:bg-error-focus active:bg-error-focus/90">
                                            <i class="fa-regular fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <input
                                    class="form-switch is-outline h-3.5 w-7 rounded-full border border-slate-400/70 bg-slate-100 before:rounded-full before:bg-slate-300 checked:!border-success checked:before:!bg-success dark:border-navy-500 dark:bg-navy-900 dark:before:bg-navy-400"
                                    x-on:click.prevent="$dialogs.confirmAction({
                                    title: 'Enable/Disable Service?',
                                    onConfirm: () => {
                                        window.Livewire.find('<?php echo e($_instance->getId()); ?>').switchServiceOnAndOff(service.id, service.is_active).then((result) => {
                                            service.is_active = result;
                                            console.log(result);
                                        })
                                    }})"
                                    x-bind:checked="service.is_active" type="checkbox" />
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </main>

    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('services.service-form', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3573549486-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
</div>
<?php /**PATH J:\Laravel_Projects\alhars_last-Rev\resources\views/livewire/services/services.blade.php ENDPATH**/ ?>