<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WhatsAppMessaging implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $userId;
    public $profile_id;
    public $broadcastType;
    public $messageId;
    public $status;

    /**
     * Create a new event instance.
     */
    public function __construct($userId, $profile_id, $messageId = null, $status = 'pending', $broadcastType = 'message_received')
    {
        $this->userId = $userId;
        $this->profile_id = $profile_id;
        $this->broadcastType = $broadcastType;
        $this->messageId = $messageId;
        $this->status = $status;

        // Debug logging
        Log::info("🚀 WhatsAppMessaging event created", [
            'userId' => $userId,
            'profile_id' => $profile_id,
            'broadcastType' => $broadcastType,
            'messageId' => $messageId,
            'channel' => 'whatsapp.' . $userId
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('whatsapp.' . $this->userId),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'whatsapp.messaging';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'profile_id' => $this->profile_id,
            'type' => $this->broadcastType,
            'message_id' => $this->messageId,
            'status' => $this->status
        ];
    }

    public function broadcastWhen(): bool
    {
        return true;
    }
}
