<?php

namespace App\Http\View\Composers;

use App\Main\SidebarPanel;
use Illuminate\View\View;

class SidebarComposer
{
    /**
     * Bind data to the view.
     *
     * @param View $view
     * @return void
     */
    public function compose(View $view)
    {

        if (!auth()->check() || is_null(request()->route())) {
            return;
        }

        $pageName = request()->route()->getName();
        $routePrefix = explode('/', $pageName)[0] ?? '';

        switch ($routePrefix) {
            case 'social':
                $view->with('viewData', SidebarPanel::bots());
                break;
            case 'clinics':
                $view->with('viewData', SidebarPanel::clinics());
                break;
            case 'doctors':
                $view->with('viewData', SidebarPanel::doctors());
                break;
            case 'services':
                $view->with('viewData', SidebarPanel::services());
                break;
            case 'appointments':
                $view->with('viewData', SidebarPanel::appointments());
                break;
        }

        $view->with('pageName', $pageName);
        $view->with('routePrefix', $routePrefix);
    }
}
