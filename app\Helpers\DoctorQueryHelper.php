<?php

namespace App\Helpers;

use App\Services\BusinessServices\DoctorService;
use Illuminate\Support\Facades\Log;

class DoctorQueryHelper
{
    protected $doctorService;

    public function __construct()
    {
        $this->doctorService = new DoctorService();
    }

    /**
     * البحث عن طبيب معين بالاسم في العمل الخاص بالمستخدم
     */

    public function findDoctorByName(string $doctorName, int $userId): array
    {
        try {
            $doctors = $this->doctorService->searchDoctorsByNameForUser($doctorName, $userId);

            if ($doctors->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لم أجد طبيب بهذا الاسم: {$doctorName}"
                ];
            }

            $doctorInfo = [];
            foreach ($doctors as $doctor) {
                $doctorInfo[] = [
                    'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                    'specialization' => $doctor->specialization,
                    'clinic' => $doctor->clinic->name ?? 'غير محدد',
                    'available' => $doctor->is_available ? 'متاح' : 'غير متاح',
                    'experience' => $doctor->experience ?? 'غير محدد',
                    'education' => $doctor->education ?? 'غير محدد'
                ];
            }

            return [
                'found' => true,
                'doctors' => $doctorInfo,
                'count' => count($doctorInfo)
            ];
            
        } catch (\Exception $e) {
            Log::error('Error finding doctor by name: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في البحث عن الطبيب'
            ];
        }
    }

    /**
     * البحث عن أطباء قسم معين للمستخدم المحدد
     */
    public function getDoctorsBySpecialization(string $specialization, int $userId): array
    {
        try {
            $doctors = $this->doctorService->getDoctorsBySpecializationForUser($specialization, $userId);

            if ($doctors->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لا يوجد أطباء في تخصص: {$specialization}"
                ];
            }

            $doctorsList = [];
            foreach ($doctors as $doctor) {
                $doctorsList[] = [
                    'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                    'clinic' => $doctor->clinic->name ?? 'غير محدد',
                    'available' => $doctor->is_available ? 'متاح' : 'غير متاح',
                    'experience' => $doctor->experience ?? 'غير محدد'
                ];
            }

            return [
                'found' => true,
                'specialization' => $specialization,
                'doctors' => $doctorsList,
                'count' => count($doctorsList)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting doctors by specialization: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في البحث عن أطباء التخصص'
            ];
        }
    }

    /**
     * التحقق من وجود طبيب معين للمستخدم المحدد
     */
    public function checkDoctorExists(string $doctorName, int $userId): array
    {
        try {
            $result = $this->findDoctorByName($doctorName, $userId);

            if ($result['found']) {
                return [
                    'exists' => true,
                    'message' => "نعم، د. {$doctorName} موجود لدينا",
                    'doctors' => $result['doctors']
                ];
            } else {
                return [
                    'exists' => false,
                    'message' => "عذراً، لا يوجد طبيب بهذا الاسم: {$doctorName}"
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error checking doctor existence: ' . $e->getMessage());
            return [
                'exists' => false,
                'message' => 'حدث خطأ في التحقق من وجود الطبيب'
            ];
        }
    }

    /**
     * الحصول على جميع التخصصات المتاحة
     */
    public function getAllSpecializations(): array
    {
        try {
            $specializations = $this->doctorService->getAllSpecializations();
            
            return [
                'specializations' => $specializations,
                'count' => count($specializations)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting specializations: ' . $e->getMessage());
            return [
                'specializations' => [],
                'count' => 0
            ];
        }
    }

    /**
     * الحصول على أطباء متاحين فقط للمستخدم المحدد
     */
    public function getAvailableDoctors(int $userId, int $limit = 10): array
    {
        try {
            $doctors = $this->doctorService->getAvailableDoctorsForUser($userId, $limit);

            $doctorsList = [];
            foreach ($doctors as $doctor) {
                $doctorsList[] = [
                    'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                    'specialization' => $doctor->specialization,
                    'clinic' => $doctor->clinic->name ?? 'غير محدد'
                ];
            }

            return [
                'doctors' => $doctorsList,
                'count' => count($doctorsList)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting available doctors: ' . $e->getMessage());
            return [
                'doctors' => [],
                'count' => 0
            ];
        }
    }
}
