<div class="contents">
    <!--[if BLOCK]><![endif]--><?php if(isset($header)): ?>
        <div class="flex items-center justify-between space-x-4 py-5 lg:py-6">
            <div class="flex items-center space-x-4">
                <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl">
                    <a class="text-primary transition-colors hover:text-primary-focus dark:text-accent-light dark:hover:text-accent"
                        href="javascript:history.back()"><?php echo e($header['title']); ?></a>
                </h2>
                <div class="h-full py-1 sm:flex">
                    <div class="h-full w-px bg-slate-300 dark:bg-navy-600"></div>
                </div>
                <ul class="flex-wrap items-center space-x-2 sm:flex">
                    <li><?php echo e($header['subtitle']); ?></li>
                </ul>
            </div>

            <a wire:navigate href="<?php echo e(route('doctors/new-doctor')); ?>"
                class="btn lg:space-x-2 bg-gradient-to-br from-purple-500 to-indigo-600 font-medium text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span class="hidden lg:inline">إضافة طبيب</span>
            </a>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-5 lg:grid-cols-3 lg:gap-6 xl:grid-cols-4">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $doctors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $doctor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card p-4 sm:p-5">
                <div class="flex items-center space-x-3">
                    <!-- Profile Image -->
                    <div class="relative">
                        <!--[if BLOCK]><![endif]--><?php if(isset($doctor['profile_image']) && $doctor['profile_image']): ?>
                            <img class="size-16 rounded-full object-cover border-2 border-slate-200 dark:border-navy-600"
                                 src="<?php echo e(asset('storage/' . $doctor['profile_image'])); ?>"
                                 alt="صورة <?php echo e($doctor['name']); ?>" />
                        <?php else: ?>
                            <div class="size-16 rounded-full bg-slate-200 dark:bg-navy-600 flex items-center justify-center border-2 border-slate-200 dark:border-navy-600">
                                <i class="fas fa-user-md text-2xl text-slate-400 dark:text-navy-300"></i>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Status Indicator -->
                        <div class="absolute -bottom-1 -right-1">
                            <!--[if BLOCK]><![endif]--><?php if($doctor['is_available']): ?>
                                <div class="size-5 rounded-full bg-success border-2 border-white dark:border-navy-700 flex items-center justify-center">
                                    <i class="fas fa-check text-white text-xs"></i>
                                </div>
                            <?php else: ?>
                                <div class="size-5 rounded-full bg-error border-2 border-white dark:border-navy-700 flex items-center justify-center">
                                    <i class="fas fa-times text-white text-xs"></i>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <!-- Doctor Info -->
                    <div class="flex-1">
                        <h3 class="text-base font-medium text-slate-700 dark:text-navy-100">
                            د. <?php echo e($doctor['name']); ?>

                        </h3>
                        <p class="text-sm text-slate-500 dark:text-navy-300"><?php echo e($doctor['specialization']); ?></p>
                        <div class="flex items-center mt-1 space-x-2">
                            <span class="badge rounded-full bg-info/10 text-info text-xs"><?php echo e($doctor['shift']); ?></span>
                            <!--[if BLOCK]><![endif]--><?php if(isset($doctor['experience']) && $doctor['experience']): ?>
                                <span class="text-xs text-slate-400"><?php echo e($doctor['experience']); ?></span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>

                <!-- Languages -->
                <!--[if BLOCK]><![endif]--><?php if(isset($doctor['languages']) && is_array($doctor['languages']) && count($doctor['languages']) > 0): ?>
                    <div class="mt-3">
                        <p class="text-xs font-medium text-slate-600 dark:text-navy-100 mb-1">اللغات:</p>
                        <div class="flex flex-wrap gap-1">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $doctor['languages']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="badge rounded-full bg-primary/10 text-primary text-xs"><?php echo e($language); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Working Days -->
                <!--[if BLOCK]><![endif]--><?php if(isset($doctor['working_days']) && is_array($doctor['working_days']) && count($doctor['working_days']) > 0): ?>
                    <div class="mt-3">
                        <p class="text-xs font-medium text-slate-600 dark:text-navy-100 mb-1">أيام العمل:</p>
                        <div class="flex flex-wrap gap-1">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $doctor['working_days']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $dayNames = [
                                        'SUN' => 'الأحد', 'MON' => 'الاثنين', 'TUE' => 'الثلاثاء',
                                        'WED' => 'الأربعاء', 'THU' => 'الخميس', 'FRI' => 'الجمعة', 'SAT' => 'السبت'
                                    ];
                                ?>
                                <span class="badge rounded-full bg-success/10 text-success text-xs">
                                    <?php echo e($dayNames[$day] ?? $day); ?>

                                </span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Actions -->
                <div class="mt-4 flex items-center justify-between space-x-2">
                    <div class="flex items-center space-x-2">
                        <!-- Availability Toggle -->
                        <label class="inline-flex items-center">
                            <input wire:click="switchDoctorOnAndOff(<?php echo e($doctor['id']); ?>, <?php echo e($doctor['is_available'] ? 'true' : 'false'); ?>)"
                                   type="checkbox"
                                   <?php echo e($doctor['is_available'] ? 'checked' : ''); ?>

                                   class="form-switch is-outline h-4 w-8 rounded-full border border-slate-400/70 bg-slate-100 before:rounded-full before:bg-slate-300 checked:!border-success checked:before:!bg-success dark:border-navy-500 dark:bg-navy-900 dark:before:bg-navy-400" />
                            <span class="ml-2 text-xs"><?php echo e($doctor['is_available'] ? 'متاح' : 'غير متاح'); ?></span>
                        </label>
                    </div>

                    <div class="flex items-center space-x-1">
                        <!-- Edit Button -->
                        <a href="<?php echo e(route('doctors/edit-doctor', ['doctor_id' => $doctor['id']])); ?>"
                           class="btn size-8 rounded-full bg-slate-150 p-0 font-medium text-slate-800 hover:bg-slate-200 focus:bg-slate-200 active:bg-slate-200/80 dark:bg-navy-500 dark:text-navy-50 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                            <i class="fas fa-edit"></i>
                        </a>

                        <!-- View Details Button -->
                        <a href="<?php echo e(route('doctors/doctor', ['doctor_id' => $doctor['id']])); ?>"
                           class="btn size-8 rounded-full bg-primary p-0 font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>

                <!-- Unavailability Reason -->
                <!--[if BLOCK]><![endif]--><?php if(!$doctor['is_available'] && isset($doctor['unavailability_reason']) && $doctor['unavailability_reason']): ?>
                    <div class="mt-2 p-2 bg-error/10 rounded-lg">
                        <p class="text-xs text-error">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <?php echo e($doctor['unavailability_reason']); ?>

                        </p>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH J:\Laravel_Projects\alhars_last-Rev\resources\views/livewire/doctors/doctors.blade.php ENDPATH**/ ?>