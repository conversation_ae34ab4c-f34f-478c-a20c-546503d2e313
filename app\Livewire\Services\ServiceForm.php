<?php

namespace App\Livewire\Services;

use App\Models\ClinicService;
use Livewire\Attributes\On;
use Livewire\Component;

class ServiceForm extends Component
{

    public $showModal = false;

    public $service_id;
    public $clinic_id;
    public $name;
    public $description;
    public $price;
    public $service_url;
    public $is_active;


    #[On('showServiceForm')]
    public function showForm($service = null)
    {
        $this->reset();
        $this->showModal = true;

        if ($service) {
            $this->service_id = $service['id'];
            $this->name = $service['name'];
            $this->description = $service['description'];
            $this->price = $service['price'];
            $this->service_url = $service['service_url'];
            $this->is_active = $service['is_active'];
        }
    }

    public function save()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric',
            'service_url' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        try {
            if ($this->service_id) {
                ClinicService::where('id', $this->service_id)->update([
                    'name' => $this->name,
                    'description' => $this->description,
                    'price' => $this->price,
                    'service_url' => $this->service_url,
                    'is_active' => $this->is_active,
                ]);
            } else {
                ClinicService::create([
                    'clinic_id' => $this->clinic_id,
                    'name' => $this->name,
                    'description' => $this->description,
                    'price' => $this->price,
                    'service_url' => $this->service_url,
                    'is_active' => $this->is_active,
                ]);
            }
        } catch (\Throwable $th) {
            return dispatch('error');
        }

        return dispatch('success');
    }

    public function render()
    {
        return view('livewire.services.service-form');
    }
}
