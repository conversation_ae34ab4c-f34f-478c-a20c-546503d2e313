<?php

namespace App\Livewire\Services;

use App\Models\Clinic;
use App\Models\ClinicService;
use Livewire\Attributes\On;
use Livewire\Component;

class ServiceForm extends Component
{

    public $showModal = false;
    public $commingFromClinic = false;
    public $clinics;
    
    public $service_id;
    public $clinic_id;
    public $name;
    public $description;
    public $price;
    public $service_url;
    public $is_active = false;

    private function resetFields()
    {
        $this->service_id = null;
        $this->clinic_id = null;
        $this->name = null;
        $this->description = null;
        $this->price = null;
        $this->service_url = null;
        $this->is_active = false;
    }

    #[On('showServiceForm')]
    public function showForm($service = null, $clinic_id = null)
    {

        if (!$this->clinics) {
            $this->clinics = Clinic::select('id', 'name')->get();
        }

        $this->resetFields();
        $this->showModal = true;

        if ($clinic_id) {
            $this->commingFromClinic = true;
            $this->clinic_id = $clinic_id;
        }

        if ($service) {
            $this->service_id = $service['id'];
            $this->name = $service['name'];
            $this->description = $service['description'];
            $this->price = $service['price'];
            $this->service_url = $service['service_url'];
            $this->is_active = $service['is_active'];
        }
    }

    public function save()
    {
        $this->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'price' => 'required|numeric',
            'service_url' => 'nullable|string',
            'clinic_id' => 'required|integer',
        ]);

        try {
            if ($this->service_id) {
                ClinicService::where('id', $this->service_id)->update([
                    'name' => $this->name,
                    'description' => $this->description,
                    'price' => $this->price,
                    'service_url' => $this->service_url,
                    'is_active' => $this->is_active,
                ]);
            } else {
                ClinicService::create([
                    'clinic_id' => $this->clinic_id,
                    'name' => $this->name,
                    'description' => $this->description,
                    'price' => $this->price,
                    'service_url' => $this->service_url,
                    'is_active' => $this->is_active,
                ]);
            }
        } catch (\Throwable $th) {
            $this->dispatch('show-error', 'Failed to save service');
            return;
        }

        // هذا عشان يحدث الخدمات بشكل صحيح
        // لو فاتح التعديل من العيادة لازم نمرر رقم العيادة عشان يرجع خدمات العيادة فقط
        $this->dispatch('service-saved', $this->commingFromClinic ? $this->clinic_id : null);

        $this->showModal = false;
        $this->dispatch('show-success', 'Service saved successfully');
    }

    public function render()
    {
        return view('livewire.services.service-form');
    }
}
