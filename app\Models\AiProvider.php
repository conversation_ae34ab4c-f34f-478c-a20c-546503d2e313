<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\AiProviderApiKey;
use App\Models\AiModel;

class Ai<PERSON>rovider extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'base_url',
        'status',
    ];

    public function apiKeys()
    {
        return $this->hasMany(AiProviderApiKey::class);
    }

    public function models()
    {
        return $this->hasMany(AiModel::class);
    }
}
