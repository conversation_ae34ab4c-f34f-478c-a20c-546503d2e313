<?php

namespace App\Livewire\Settings\Socials;

use App\Models\WhatsApp\WhatsAppProfile;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\WhatsApp\WhatsAppSettings as WhatsAppSettingsModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class WhatsappSettings extends Component
{
    public $auto_reply = false;
    public $welcome_message = '';
    public $connected_ai_model = null;
    public $whatsapp_settings_id = null;
    public $client_profile = null;

    // Available AI models (placeholder - you can populate from your AI models)
    public $available_ai_models = [
        1 => 'GPT-4',
        2 => 'Claude 3',
        3 => 'Gemini Pro',
        4 => 'Custom Model'
    ];

    #[On('loadSettings')]
    public function loadSettings()
    {
        $user = Auth::user();
        if ($user) {
            // FAST: Load settings with connection info from database first
            $settings = WhatsAppSettingsModel::forUserWithConnection($user->id);

            if ($settings) {
                // Load basic settings
                $this->auto_reply = $settings->auto_reply;
                $this->welcome_message = $settings->welcome_message ?? '';
                $this->connected_ai_model = $settings->connected_ai_model;
                $this->whatsapp_settings_id = $settings->id;

                // FAST: Check if user is connected from database
                $client_profile = $settings->clientProfile;
                if ($client_profile) {
                    $this->client_profile = [
                        'whatsapp_id' => $client_profile->whatsapp_id,
                        'phone_number' => $client_profile->phone_number,
                        'profile_name' => $client_profile->profile_name,
                        'profile_picture_url' => $client_profile->profile_picture_url,
                        'is_client' => $client_profile->is_client,
                        'platform' => $client_profile->platform,
                    ];
                } else {
                    $this->client_profile = null;
                }
            } else {
                $settings = WhatsAppSettingsModel::updateOrCreateForUser($user->id, [
                    'auto_reply' => false,
                    'welcome_message' => '',
                    'connected_ai_model' => null,
                ]);

                $this->whatsapp_settings_id = $settings->id;

                // Also update the component properties
                $this->auto_reply = $settings->auto_reply;
                $this->welcome_message = $settings->welcome_message ?? '';
                $this->connected_ai_model = $settings->connected_ai_model;
            }
        }
    }

    /**
     * Update device info (called when user gets authenticated)
     */
    
    #[On('whatsapp-authenticated')]
    public function updateDeviceInfo()
    {
        try {
            $userId = Auth::id();

            if (!$this->client_profile) {
                Log::warning('No device info received in updateDeviceInfo method');
                return;
            }
            
            // Save the connected user information
            $client_profile = WhatsAppProfile::updateOrCreateFromClientInfo(
                $this->whatsapp_settings_id,
                $this->client_profile
            );

            Log::info('Connected WhatsApp user saved successfully', [
                'user_id' => $userId,
                'client_profile_id' => $client_profile->id
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving connected WhatsApp user: ' . $e->getMessage(), [
                'user_id' => $userId ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function saveSettings()
    {
        $this->validate([
            'welcome_message' => 'nullable|string|max:1000',
            'connected_ai_model' => 'nullable|integer',
        ]);

        $user = Auth::user();
        if ($user) {
            WhatsAppSettingsModel::updateOrCreateForUser($user->id, [
                'auto_reply' => $this->auto_reply,
                'welcome_message' => $this->welcome_message,
                'connected_ai_model' => $this->connected_ai_model,
            ]);

            $this->dispatch('settings-saved', 'WhatsApp settings saved successfully!');
        }
    }

    public function mount()
    {
        $this->dispatch('loadSettings');
    }


    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}
