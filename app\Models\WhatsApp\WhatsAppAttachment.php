<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppAttachment extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'attachments';
    public $timestamps = false;

    protected $fillable = [
        'message_id',
        'filename',
        'mime_type',
        'file_size',
    ];


    /**
     * Get the message that owns this attachment
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(WhatsAppMessage::class, 'message_id');
    }


    /**
     * Get the file URL for this attachment
     */
    public function getFileUrlAttribute(): string
    {
        return route('whatsapp.attachment', ['filename' => $this->filename]);
    }

    /**
     * Check if the attachment is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if the attachment is a video
     */
    public function isVideo(): bool
    {
        return str_starts_with($this->mime_type, 'video/');
    }

    /**
     * Check if the attachment is audio
     */
    public function isAudio(): bool
    {
        return str_starts_with($this->mime_type, 'audio/');
    }

    /**
     * Check if the attachment is a document
     */
    public function isDocument(): bool
    {
        return !$this->isImage() && !$this->isVideo() && !$this->isAudio();
    }
}
