<?php

namespace App\Data\WhatsApp;

class WhatsappBtns
{
    public ?string $header;
    public ?string $footer;
    public array $buttons;

    public function __construct(array $buttons, ?string $header = null, ?string $footer = null)
    {
        $this->header = $header;
        $this->footer = $footer;
        $this->buttons = $buttons;
    }

    public function toArray(): array
    {
        return [
            'header' => $this->header,
            'footer' => $this->footer,
            'buttons' => $this->buttons,
        ];
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }
}