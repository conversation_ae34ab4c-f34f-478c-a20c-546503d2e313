<?php

namespace App\Livewire\Doctors;

use App\Models\Doctor;
use Livewire\Component;
use Carbon\Carbon;

class Doctors extends Component
{
    public $doctors;
    public $header = ['title' => 'Doctors', 'subtitle' => 'All Doctors'];


    public function mount($clinic_id = null)
    {
        if ($clinic_id) {
            $this->doctors = Doctor::where('clinic_id', $clinic_id)
                ->with(['appointments' => function ($query) {
                    $query->whereDate('appointment_date', Carbon::today())
                        ->select('id', 'status', 'doctor_id');
                }])
                ->orderBy('first_name')
                ->get();
            $clinicName = $this->doctors->first()->clinic->name;
            $this->header = ['title' => $clinicName . ' | Doctors', 'subtitle' => $clinicName . ' > Doctors'];
            return;
        }
        $this->doctors = Doctor::all();
    }

    public function render()
    {
        return view('livewire.doctors.doctors')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => $this->header['title'],
            ]
        );
    }
}
