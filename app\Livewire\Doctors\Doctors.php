<?php

namespace App\Livewire\Doctors;

use App\Models\Doctor;
use Livewire\Component;
use Carbon\Carbon;

class Doctors extends Component
{
    public $doctors;
    public $header = ['title' => 'Doctors', 'subtitle' => 'All Doctors'];


    public function switchDoctorOnAndOff($doctor_id, $is_available)
    {
        $updated = Doctor::where('id', $doctor_id)->update(['is_available' => !$is_available]);
        if ($updated) {
            // Update the local array
            foreach ($this->doctors as &$doctor) {
                if ($doctor['id'] == $doctor_id) {
                    $doctor['is_available'] = !$is_available;
                    break;
                }
            }
            $this->dispatch('show-success', $is_available ? 'تم إيقاف الطبيب' : 'تم تفعيل الطبيب');
        }
    }

    public function mount($clinic_id = null)
    {
        if ($clinic_id) {
            $this->doctors = Doctor::where('clinic_id', $clinic_id)
                ->with(['clinic'])
                ->orderBy('name')
                ->get()
                ->toArray();

            if (count($this->doctors) > 0) {
                $clinicName = $this->doctors[0]['clinic']['name'];
                $this->header = ['title' => $clinicName . ' | الأطباء', 'subtitle' => $clinicName . ' > الأطباء'];
            }
            return;
        }
        $this->doctors = Doctor::with(['clinic'])->orderBy('name')->get()->toArray();
    }

    public function render()
    {
        return view('livewire.doctors.doctors')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => $this->header['title'],
            ]
        );
    }
}
