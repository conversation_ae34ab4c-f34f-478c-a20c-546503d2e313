import Swal from "sweetalert2";

/**
 * Global dialog and notification functions
 */

// Confirmation dialog for deleting items
function confirmDelete(options) {
    const defaults = {
        title: 'Delete Item?',
        text: 'Are you sure you want to delete this item?',
        description: null,
        icon: 'warning',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        onConfirm: null,
        itemId: null,
        eventName: 'deleteItem',
        dataKey: 'itemId'
    };

    // Merge defaults with provided options
    const settings = { ...defaults, ...options };

    // Build HTML content
    let htmlContent = settings.text;
    if (settings.description) {
        htmlContent += `<br><br><small class="text-muted">"${settings.description.substring(0, 100)}${settings.description.length > 100 ? '...' : ''}"</small>`;
    }

    // Show SweetAlert dialog
    Swal.fire({
        title: settings.title,
        html: htmlContent,
        icon: settings.icon,
        showCancelButton: true,
        confirmButtonColor: settings.confirmButtonColor,
        cancelButtonColor: settings.cancelButtonColor,
        confirmButtonText: settings.confirmButtonText,
        cancelButtonText: settings.cancelButtonText,
        reverseButtons: true,
        focusCancel: true,
        backdrop: true,
        showLoaderOnConfirm: true,
        preConfirm: () => {
            // Show loading state immediately
            Swal.showLoading();

            // Add a small delay to ensure the loading state is visible
            return new Promise((resolve) => {
                setTimeout(() => {
                    if (typeof settings.onConfirm === 'function') {
                        // Execute the provided callback function
                        const result = settings.onConfirm(settings);
                        resolve(result);
                    } else {
                        resolve(true);
                    }
                }, 300); // Short delay to ensure loading state is visible
            });
        }
    });
}

// Generic confirmation dialog
function confirmAction(options) {
    const defaults = {
        title: 'Confirm Action',
        text: 'Are you sure you want to proceed?',
        icon: 'question',
        confirmButtonText: 'Yes, proceed',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#6c757d',
        onConfirm: null
    };

    // Merge defaults with provided options
    const settings = { ...defaults, ...options };

    // Show SweetAlert dialog
    Swal.fire({
        title: settings.title,
        html: settings.text,
        icon: settings.icon,
        showCancelButton: true,
        confirmButtonColor: settings.confirmButtonColor,
        cancelButtonColor: settings.cancelButtonColor,
        confirmButtonText: settings.confirmButtonText,
        cancelButtonText: settings.cancelButtonText,
        reverseButtons: true,
        focusCancel: true,
        backdrop: true,
        showLoaderOnConfirm: !!settings.onConfirm,
        preConfirm: () => {
            if (typeof settings.onConfirm === 'function') {
                settings.onConfirm();
            }
            return settings.closeOnConfirm !== false;
        }
    });
}

// Information dialog
function showInfo(options) {
    const defaults = {
        title: 'Information',
        text: '',
        icon: 'info',
        confirmButtonText: 'OK',
        confirmButtonColor: '#3085d6'
    };

    // Merge defaults with provided options
    const settings = { ...defaults, ...options };

    // Show SweetAlert dialog
    Swal.fire({
        title: settings.title,
        html: settings.text,
        icon: settings.icon,
        confirmButtonColor: settings.confirmButtonColor,
        confirmButtonText: settings.confirmButtonText
    });
}

// Create dialogs object to export
const dialogs = {
    confirmDelete,
    confirmAction,
    showInfo
};

// Make functions globally available
window.confirmDelete = confirmDelete;
window.confirmAction = confirmAction;
window.showInfo = showInfo;

// Listen for dialog events from Livewire
window.addEventListener('show-dialog', function (event) {
    const { message, type = 'info', title, options = {} } = event.detail[0];
    console.log('Dialog:', message, type);

    switch (type) {
        case 'confirm-delete':
            confirmDelete({
                title: title || 'Delete Item?',
                text: message,
                ...options
            });
            break;
        case 'confirm':
            confirmAction({
                title: title || 'Confirm Action',
                text: message,
                ...options
            });
            break;
        case 'info':
        default:
            showInfo({
                title: title || 'Information',
                text: message,
                ...options
            });
            break;
    }
});

// Listen for toast events to close any open SweetAlert dialog
window.addEventListener('toast-success', event => {
    if (Swal.isVisible()) {
        Swal.close();
    }
});

window.addEventListener('toast-error', event => {
    if (Swal.isVisible()) {
        Swal.close();
    }
});

window.addEventListener('toast-warning', event => {
    if (Swal.isVisible()) {
        Swal.close();
    }
});

window.addEventListener('toast-info', event => {
    if (Swal.isVisible()) {
        Swal.close();
    }
});


// Export the dialogs object as default
export default dialogs;