<?php

namespace App\Livewire\Clinics;

use App\Models\Appointment;
use App\Models\Clinic;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class Clinics extends Component
{
    public $clinics;
    
    public function mount($clinics = null)
    {
        if ($clinics) {
            $this->clinics = $clinics;
            return;
        }
        $this->clinics = Clinic::all();
    }

    public function render()
    {
        return view('livewire.clinics.clinics');
    }
}
