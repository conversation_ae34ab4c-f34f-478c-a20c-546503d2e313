import Toastify from "toastify-js";

// Toast utility functions - للاستخدام مع Livewire dispatches
const toast = {
    // الطريقة الأساسية لعرض التوست
    show: function (message, type = 'success') {
        let backgroundColor;
        switch (type) {
            case 'success':
                backgroundColor = 'linear-gradient(to right, #00b09b, #96c93d)';
                break;
            case 'error':
                backgroundColor = 'linear-gradient(to right, #ff5f6d, #ffc371)';
                break;
            case 'warning':
                backgroundColor = 'linear-gradient(to right, #f093fb, #f5576c)';
                break;
            case 'info':
                backgroundColor = 'linear-gradient(to right, #4facfe, #00f2fe)';
                break;
            default:
                backgroundColor = 'linear-gradient(to right, #00b09b, #96c93d)';
        }

        Toastify({
            text: message,
            duration: 3000,
            gravity: "top",
            position: "right",
            style: {
                background: backgroundColor,
            },
            stopOnFocus: true
        }).showToast();
    },

    success: function (message) {
        this.show(message, 'success');
    },

    error: function (message) {
        this.show(message, 'error');
    },

    warning: function (message) {
        this.show(message, 'warning');
    },

    info: function (message) {
        this.show(message, 'info');
    }
};

// Listen for toast notifications from Livewire
window.addEventListener('show-success', function (event) {
    toast.show(event.detail[0], 'success');
});

window.addEventListener('show-error', function (event) {
    toast.show(event.detail[0], 'error');
});

window.addEventListener('show-warning', function (event) {
    toast.show(event.detail[0], 'warning');
});

window.addEventListener('show-info', function (event) {
    toast.show(event.detail[0], 'info');
});

window.addEventListener('show-toast', function (event) {
    toast.show(event.detail[0], type);
});

// Listen for console log events from Livewire
window.addEventListener('console-log', function (event) {
    const { title, data = {} } = event.detail[0];

    console.log(`${title}:`, data);
});

// Export the toast object as default
export default toast;