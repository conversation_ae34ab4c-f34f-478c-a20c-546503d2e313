<?php

namespace App\Livewire\Services;

use App\Models\ClinicService;
use Livewire\Component;

class ServicesHome extends Component
{
    public $services;
    public $header = ['title' => 'Services', 'subtitle' => 'All Services'];

    public function switchServiceOnAndOff($service_id, $is_active)
    {
        ClinicService::where('id', $service_id)->update(['is_active' => !$is_active]);
    }

    public function deleteService($service_id)
    {
        ClinicService::where('id', $service_id)->delete();
    }

    public function mount($services = null)
    {
        if ($services) {
            $this->services = $services;
            return;
        }
        $this->services = ClinicService::all();
    }

    public function render()
    {
        return view('livewire.services.services-home')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Services',
            ]
        );
    }
}
