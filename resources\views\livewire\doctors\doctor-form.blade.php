<div class="contents">
    <main class="main-content w-full pb-8">
        <!-- Header -->
        <div class="flex items-center justify-between space-x-4 py-5 lg:py-6 px-[var(--margin-x)]">
            <div class="flex items-center space-x-4">
                <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl">
                    <a class="text-primary transition-colors hover:text-primary-focus dark:text-accent-light dark:hover:text-accent"
                        href="javascript:history.back()">
                        {{ $doctor_id ? 'تعديل بيانات الطبيب' : 'إضافة طبيب جديد' }}
                    </a>
                </h2>
                <div class="h-full py-1 sm:flex">
                    <div class="h-full w-px bg-slate-300 dark:bg-navy-600"></div>
                </div>
                <ul class="flex-wrap items-center space-x-2 sm:flex">
                    <li>{{ $doctor_id ? 'تحديث البيانات' : 'إدخال بيانات جديدة' }}</li>
                </ul>
            </div>
        </div>

        <!-- Form Container -->
        <div class="px-[var(--margin-x)]">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 lg:gap-6">

                <!-- Profile Image Section -->
                <div class="lg:col-span-1">
                    <div class="card p-6">
                        <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100 mb-4">الصورة الشخصية</h3>

                        <div class="flex flex-col items-center space-y-4">
                            <!-- Current Image Display -->
                            <div class="relative">
                                @if($current_profile_image)
                                    <img src="{{ asset('storage/' . $current_profile_image) }}"
                                         alt="صورة الطبيب"
                                         class="size-32 rounded-full object-cover border-4 border-slate-200 dark:border-navy-600">
                                @else
                                    <div class="size-32 rounded-full bg-slate-200 dark:bg-navy-600 flex items-center justify-center">
                                        <i class="fas fa-user-md text-4xl text-slate-400 dark:text-navy-300"></i>
                                    </div>
                                @endif

                                <!-- Upload overlay -->
                                <label for="profile_image" class="absolute inset-0 rounded-full bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
                                    <i class="fas fa-camera text-white text-xl"></i>
                                </label>
                            </div>

                            <!-- File Input -->
                            <input type="file"
                                   id="profile_image"
                                   wire:model="profile_image"
                                   accept="image/*"
                                   class="hidden">

                            <!-- Upload Button -->
                            <label for="profile_image" class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 cursor-pointer">
                                <i class="fas fa-upload mr-2"></i>
                                {{ $current_profile_image ? 'تغيير الصورة' : 'رفع صورة' }}
                            </label>

                            @if($profile_image)
                                <div class="text-sm text-green-600 dark:text-green-400">
                                    <i class="fas fa-check mr-1"></i>
                                    تم اختيار صورة جديدة
                                </div>
                            @endif

                            @error('profile_image')
                                <div class="text-sm text-error">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Main Form Section -->
                <div class="lg:col-span-2">
                    <form wire:submit.prevent="save" class="space-y-6">

                        <!-- Basic Information Card -->
                        <div class="card p-6">
                            <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100 mb-4">المعلومات الأساسية</h3>

                            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                <!-- Name -->
                                <label class="block">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">اسم الطبيب *</span>
                                    <input wire:model="name"
                                           class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent @error('name') border-error @enderror"
                                           placeholder="أدخل اسم الطبيب"
                                           type="text" />
                                    @error('name') <span class="text-xs text-error">{{ $message }}</span> @enderror
                                </label>

                                <!-- Clinic -->
                                <label class="block">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">العيادة *</span>
                                    <select wire:model="clinic_id"
                                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent @error('clinic_id') border-error @enderror">
                                        <option value="">اختر العيادة</option>
                                        @foreach($clinics as $clinic)
                                            <option value="{{ $clinic->id }}">{{ $clinic->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('clinic_id') <span class="text-xs text-error">{{ $message }}</span> @enderror
                                </label>

                                <!-- Specialization -->
                                <label class="block">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">التخصص *</span>
                                    <select wire:model="specialization"
                                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent @error('specialization') border-error @enderror">
                                        <option value="">اختر التخصص</option>
                                        @foreach($this->specializations as $spec)
                                            <option value="{{ $spec }}">{{ $spec }}</option>
                                        @endforeach
                                    </select>
                                    @error('specialization') <span class="text-xs text-error">{{ $message }}</span> @enderror
                                </label>

                                <!-- Experience -->
                                <label class="block">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">سنوات الخبرة</span>
                                    <input wire:model="experience"
                                           class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                           placeholder="مثال: 5 سنوات"
                                           type="text" />
                                    @error('experience') <span class="text-xs text-error">{{ $message }}</span> @enderror
                                </label>
                            </div>

                            <!-- Education -->
                            <label class="block mt-4">
                                <span class="text-sm font-medium text-slate-600 dark:text-navy-100">المؤهل العلمي</span>
                                <textarea wire:model="education"
                                          rows="3"
                                          class="form-textarea mt-1.5 w-full resize-none rounded-lg border border-slate-300 bg-transparent p-2.5 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                          placeholder="مثال: بكالوريوس طب الأسنان - جامعة الملك سعود"></textarea>
                                @error('education') <span class="text-xs text-error">{{ $message }}</span> @enderror
                            </label>
                        </div>

                        <!-- Certifications Card -->
                        <div class="card p-6">
                            <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100 mb-4">الشهادات والمؤهلات</h3>

                            <div class="space-y-4">
                                <!-- Add Certification -->
                                <div class="flex space-x-2">
                                    <input wire:model="certification_input"
                                           wire:keydown.enter.prevent="addCertification"
                                           class="form-input flex-1 rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                           placeholder="أدخل شهادة أو مؤهل"
                                           type="text" />
                                    <button type="button"
                                            wire:click="addCertification"
                                            class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>

                                <!-- Certifications List -->
                                @if(count($certifications) > 0)
                                    <div class="space-y-2">
                                        @foreach($certifications as $index => $certification)
                                            <div class="flex items-center justify-between bg-slate-100 dark:bg-navy-600 rounded-lg px-3 py-2">
                                                <span class="text-sm">{{ $certification }}</span>
                                                <button type="button"
                                                        wire:click="removeCertification({{ $index }})"
                                                        class="text-error hover:text-error-focus">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Languages Card -->
                        <div class="card p-6">
                            <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100 mb-4">اللغات</h3>

                            <div class="space-y-4">
                                <!-- Add Language -->
                                <div class="flex space-x-2">
                                    <select wire:model="language_input"
                                            class="form-select flex-1 rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                                        <option value="">اختر لغة</option>
                                        @foreach($this->availableLanguages as $lang)
                                            @if(!in_array($lang, $languages))
                                                <option value="{{ $lang }}">{{ $lang }}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                    <button type="button"
                                            wire:click="addLanguage"
                                            class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>

                                <!-- Languages List -->
                                @if(count($languages) > 0)
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($languages as $index => $language)
                                            <div class="flex items-center bg-primary/10 text-primary rounded-full px-3 py-1 text-sm">
                                                <span>{{ $language }}</span>
                                                <button type="button"
                                                        wire:click="removeLanguage({{ $index }})"
                                                        class="ml-2 text-primary hover:text-primary-focus">
                                                    <i class="fas fa-times text-xs"></i>
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Working Schedule Card -->
                        <div class="card p-6">
                            <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100 mb-4">جدول العمل</h3>

                            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                <!-- Shift -->
                                <label class="block">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">الفترة *</span>
                                    <select wire:model="shift"
                                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                                        <option value="صباحي">صباحي</option>
                                        <option value="مسائي">مسائي</option>
                                    </select>
                                    @error('shift') <span class="text-xs text-error">{{ $message }}</span> @enderror
                                </label>

                                <!-- Availability Status -->
                                <label class="block">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">حالة التوفر</span>
                                    <div class="mt-1.5 flex items-center space-x-3">
                                        <label class="inline-flex items-center">
                                            <input wire:model="is_available"
                                                   type="checkbox"
                                                   value="1"
                                                   class="form-checkbox is-outline h-5 w-5 rounded border border-slate-400/70 bg-slate-100 before:bg-slate-500 checked:border-primary checked:before:bg-primary dark:border-navy-400 dark:bg-navy-900 dark:before:bg-navy-200 dark:checked:border-accent dark:checked:before:bg-accent" />
                                            <span class="ml-2">متاح للعمل</span>
                                        </label>
                                    </div>
                                </label>
                            </div>

                            <!-- Working Days -->
                            <div class="mt-4">
                                <span class="text-sm font-medium text-slate-600 dark:text-navy-100">أيام العمل</span>
                                <div class="mt-2 grid grid-cols-2 gap-2 sm:grid-cols-4">
                                    @foreach($this->workingDaysOptions as $day => $dayName)
                                        <label class="inline-flex items-center">
                                            <input wire:model="working_days"
                                                   type="checkbox"
                                                   value="{{ $day }}"
                                                   class="form-checkbox is-outline h-4 w-4 rounded border border-slate-400/70 bg-slate-100 before:bg-slate-500 checked:border-primary checked:before:bg-primary dark:border-navy-400 dark:bg-navy-900 dark:before:bg-navy-200 dark:checked:border-accent dark:checked:before:bg-accent" />
                                            <span class="ml-2 text-sm">{{ $dayName }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Unavailability Reason -->
                            @if(!$is_available)
                                <label class="block mt-4">
                                    <span class="text-sm font-medium text-slate-600 dark:text-navy-100">سبب عدم التوفر</span>
                                    <input wire:model="unavailability_reason"
                                           class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                           placeholder="أدخل سبب عدم التوفر"
                                           type="text" />
                                    @error('unavailability_reason') <span class="text-xs text-error">{{ $message }}</span> @enderror
                                </label>
                            @endif
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3">
                            <a href="{{ url()->previous() }}"
                               class="btn font-medium text-slate-700 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                إلغاء
                            </a>
                            <button type="submit"
                                    wire:loading.attr="disabled"
                                    class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 disabled:opacity-50">
                                <span wire:loading.remove>
                                    <i class="fas fa-save mr-2"></i>
                                    {{ $doctor_id ? 'تحديث البيانات' : 'حفظ البيانات' }}
                                </span>
                                <span wire:loading>
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    جاري الحفظ...
                                </span>
                            </button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </main>
</div>