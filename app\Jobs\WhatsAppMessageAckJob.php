<?php

namespace App\Jobs;

use App\Models\WhatsApp\WhatsAppMessage;
use App\Events\WhatsAppMessaging;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WhatsAppMessageAckJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 30;

    protected $messageId;
    protected $ackStatus;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $messageId, string $ackStatus, int $userId)
    {
        $this->messageId = $messageId;
        $this->ackStatus = $ackStatus;
        $this->userId = $userId;

        // Set queue name for message ack
        $this->onQueue('whatsapp-ack');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $message = WhatsAppMessage::where('message_id', $this->messageId)->first();

            if (!$message) {
                Log::warning('Message not found for ACK update', [
                    'message_id' => $this->messageId,
                    'ack_status' => $this->ackStatus
                ]);
                return;
            }

            // Update message status
            $message->update(['status' => $this->ackStatus]);

            // Broadcast the event via Reverb
            try {
                broadcast(new WhatsAppMessaging(
                    $this->userId,
                    $message->profile_id,
                    $this->messageId,
                    $this->ackStatus,
                    'message_ack'
                ));
            } catch (\Exception $broadcastException) {
                Log::warning('Failed to broadcast WhatsApp ACK event', [
                    'user_id' => $this->userId,
                    'message_id' => $this->messageId,
                    'error' => $broadcastException->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp message ACK job failed', [
                'message_id' => $this->messageId,
                'ack_status' => $this->ackStatus,
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('WhatsApp message ACK job permanently failed', [
            'message_id' => $this->messageId,
            'ack_status' => $this->ackStatus,
            'user_id' => $this->userId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);
    }
}
