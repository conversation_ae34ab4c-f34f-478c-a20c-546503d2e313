<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Doctor extends Model
{

    protected $fillable = [
        'business_id',
        'clinic_id',
        'name',
        'profile_image',
        'specialization',
        'experience',
        'education',
        'certifications',
        'languages',
        'is_available',
        'unavailability_reason',
        'working_days',
        'shift',
    ];

    protected $casts = [
        'certifications' => 'array',
        'languages' => 'array',
        'is_available' => 'boolean',
        'working_days' => 'array',
    ];

    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }
    
}
