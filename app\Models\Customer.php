<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{

    protected $fillable = [
        'business_id',
        'is_loyal_customer',
        'full_name',
        'phone_number',
        'id_number',
        'address',
        'birth_date',
        'gender',
        'marital_status',
        'job_title',
        'nationality',
        'religion',
        'education',
        'family_size',
        'customer_context',
    ];

    protected $casts = [
        'is_loyal_customer' => 'boolean',
        'customer_context' => 'array',
    ];

    public function business()
    {
        return $this->belongsTo(Business::class);
    }
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function socialProfiles()
    {
        return $this->morphMany(CustomerSocialProfile::class, 'sociable');
    }

}
