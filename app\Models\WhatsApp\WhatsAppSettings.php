<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\CreatesDatabaseFile;
use App\Models\User;

class WhatsAppSettings extends Model
{
    use HasFactory, CreatesDatabaseFile;

    protected $connection = 'whatsapp';
    protected $table = 'whatsapp_settings';

    protected $fillable = [
        'user_id',
        'auto_reply',
        'welcome_message',
        'connected_ai_model',
    ];

    protected $casts = [
        'auto_reply' => 'boolean',
    ];

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // When settings are deleted, also delete all related profiles
        static::deleting(function ($settings) {
            $settings->profiles()->delete();
        });
    }

    /**
     * Get the settings for a specific user with caching
     */
    public static function forUser($userId)
    {
        return static::where('user_id', $userId)->first();
    }

    /**
     * Get the settings for a specific user with only specific fields for performance
     */
    public static function forUserSelect($userId, array $fields = ['*'])
    {
        return static::select($fields)->where('user_id', $userId)->first();
    }

    /**
     * Update or create settings with optimized query
     */
    public static function updateOrCreateForUser($userId, array $data)
    {
        return static::updateOrCreate(
            ['user_id' => $userId],
            $data
        );
    }

    /**
     * Get the Laravel user that owns these settings
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all profiles associated with these settings
     */
    public function profiles(): HasMany
    {
        return $this->hasMany(WhatsAppProfile::class, 'whatsapp_settings_id');
    }

    /**
     * Get the first connected client (for convenience) - this is the client that is connected to the user
     */
    public function clientProfile()
    {
        return $this->hasOne(WhatsAppProfile::class, 'whatsapp_settings_id')->where('is_client', true);
    }

    /**
     * Check if client is currently connected
     */
    public function isConnected()
    {
        return $this->clientProfile()->exists();
    }

    /**
     * Get settings with connection status for a user
     */
    public static function forUserWithConnection($userId)
    {
        return static::with('clientProfile')->where('user_id', $userId)->first();
    }
}
