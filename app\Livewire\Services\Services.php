<?php

namespace App\Livewire\Services;

use App\Models\ClinicService;
use Livewire\Attributes\On;
use Livewire\Component;

class Services extends Component
{
    public $services;
    public $header = ['title' => 'Services', 'subtitle' => 'All Services'];

    public function switchServiceOnAndOff($service_id, $is_active)
    {
        $service = ClinicService::where('id', $service_id)->update(['is_active' => !$is_active]);
        if ($service) {
            $this->services->where('id', $service_id)->first()->is_active = !$is_active;
            $this->dispatch('show-success', $is_active ? 'Service disabled successfully' : 'Service enabled successfully');
            return !$is_active;
        } else {
            $this->dispatch('show-error', 'Failed to update service');
            return $is_active;
        }
    }

    public function deleteService($service_id)
    {
        $service =  ClinicService::where('id', $service_id)->delete();
        if ($service) {
            $this->services->where('id', $service_id)->first()->delete();
            $this->dispatch('show-success', 'Service Deleted successfully');
            return true;
        } else {
            $this->dispatch('show-error', 'Failed to Delete service');
            return false;
        }
    }

    #[On('service-saved')]
    public function mount($clinic_id = null)
    {
        if ($clinic_id) {
            $this->services = ClinicService::with('clinic')->where('clinic_id', $clinic_id)->get();
            $clinicName = $this->services->first()->clinic->name;
            $this->header = ['title' => $clinicName . ' | Services', 'subtitle' => $clinicName . ' > Services'];
            return;
        }
        $this->services = ClinicService::all();
    }

    public function render()
    {
        return view('livewire.services.services')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => $this->header['title'],
            ]
        );
    }
}
