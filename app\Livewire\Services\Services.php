<?php

namespace App\Livewire\Services;

use App\Models\ClinicService;
use Livewire\Attributes\On;
use Livewire\Component;

class Services extends Component
{
    public $services;
    public $clinic;
    public $header = ['title' => 'Services', 'subtitle' => 'All Services'];

    public function switchServiceOnAndOff($service_id, $is_active)
    {
        $service = ClinicService::where('id', $service_id)->update(['is_active' => !$is_active]);
        if ($service) {
            // Update in the array
            foreach ($this->services as &$s) {
                if ($s['id'] == $service_id) {
                    $s['is_active'] = !$is_active;
                    break;
                }
            }
            $this->dispatch('show-success', $is_active ? 'Service disabled successfully' : 'Service enabled successfully');
            return !$is_active;
        } else {
            $this->dispatch('show-error', 'Failed to update service');
            return $is_active;
        }
    }

    public function deleteService($service_id)
    {
        $service =  ClinicService::where('id', $service_id)->delete();
        if ($service) {
            // Remove from the array
            $this->services = array_filter($this->services, function ($s) use ($service_id) {
                return $s['id'] != $service_id;
            });
            $this->services = array_values($this->services); // Re-index array
            $this->dispatch('show-success', 'Service Deleted successfully');
            return true;
        } else {
            $this->dispatch('show-error', 'Failed to Delete service');
            return false;
        }
    }

    #[On('service-saved')]
    public function mount($clinic_id = null)
    {
        if ($clinic_id) {
            $this->services = ClinicService::with('clinic')->where('clinic_id', $clinic_id)->get()->toArray();
            $this->clinic = $this->services[0]['clinic'];
            $this->header = ['title' => $this->clinic['name'] . ' | Services', 'subtitle' => $this->clinic['name'] . ' > Services'];
            return;
        }
        $this->services = ClinicService::all()->toArray();
    }

    public function render()
    {
        return view('livewire.services.services')->layout(
            'components.app-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => $this->header['title'],
            ]
        );
    }
}
